import { useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Typography,
  Alert
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import { ControllerRenderProps } from 'react-hook-form';
import { ValueColorRange, ChartConfigurationFormInput } from '../chartTypes';
import { validateColorRanges } from '../chartFunctions';
import { themeToColor } from '../../../theme';
import ValueColorRangePicker from './ValueColorRangePicker';

interface ValueColorRangeAccordionProps {
  field: ControllerRenderProps<ChartConfigurationFormInput, 'valueColorRanges'>;
  expanded: boolean;
  setExpanded: (expanded: boolean) => void;
  maxValue?: number;
}

function ValueColorRangeAccordion({
  field,
  expanded,
  setExpanded,
  maxValue = 100,
}: ValueColorRangeAccordionProps) {
  const [validationError, setValidationError] = useState<string | null>(null);

  const ranges = field.value || [];

  const handleAddRange = () => {
    const newRange: ValueColorRange = {
      minValue: ranges.length > 0 ? Math.max(...ranges.map(r => r.maxValue)) : 0,
      maxValue,
      color: themeToColor('primary.main'),
    };

    const updatedRanges = [...ranges, newRange];
    field.onChange(updatedRanges);

    // Validate after adding
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  const handleRangeChange = (index: number, updatedRange: ValueColorRange) => {
    const updatedRanges = ranges.map((range, i) =>
      i === index ? updatedRange : range
    );
    field.onChange(updatedRanges);

    // Validate after change
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  const handleDeleteRange = (index: number) => {
    const updatedRanges = ranges.filter((_, i) => i !== index);
    field.onChange(updatedRanges);

    // Validate after deletion
    const error = validateColorRanges(updatedRanges);
    setValidationError(error);
  };

  return (
    <Accordion
      expanded={expanded}
      onChange={(_, isExpanded) => setExpanded(isExpanded)}
      sx={{ mt: 2 }}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle2">
          Value-based colors
          {ranges.length > 0 && (
            <Typography component="span" variant="caption" sx={{ ml: 1, color: 'text.secondary' }}>
              ({ranges.length} range{ranges.length !== 1 ? 's' : ''})
            </Typography>
          )}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Define color ranges based on cell values. Each range maps values to specific colors.
          </Typography>

          {validationError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {validationError}
            </Alert>
          )}

          {ranges.map((range, index) => (
            <ValueColorRangePicker
              key={`range-${index}-${range.minValue}-${range.maxValue}`}
              range={range}
              index={index}
              onChange={handleRangeChange}
              onDelete={handleDeleteRange}
            />
          ))}

          <Button
            startIcon={<AddIcon />}
            onClick={handleAddRange}
            variant="outlined"
            size="small"
            sx={{ mt: 1, alignSelf: 'flex-start' }}
          >
            Add Range
          </Button>

          {ranges.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
              No color ranges defined. The heatmap will use default colors.
            </Typography>
          )}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
}

export default ValueColorRangeAccordion;
