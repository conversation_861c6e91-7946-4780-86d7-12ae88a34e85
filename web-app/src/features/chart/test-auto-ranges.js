// Manual test script for auto color range generation
// This can be run in the browser console to test the functionality

import { extractValuesFromStatsData, generateAutoColorRanges } from './chartFunctions';

// Test data that mimics real stats data structure
const testStatsData = {
  'High': {
    'Critical': { count: 5 },
    'Major': { count: 12 },
    'Minor': { count: 8 },
  },
  'Medium': {
    'Critical': { count: 3 },
    'Major': { count: 15 },
    'Minor': { count: 20 },
  },
  'Low': {
    'Critical': { count: 1 },
    'Major': { count: 7 },
    'Minor': { count: 25 },
  },
};

console.log('Testing extractValuesFromStatsData...');
const extractedValues = extractValuesFromStatsData(testStatsData);
console.log('Extracted values:', extractedValues);
// Expected: [5, 12, 8, 3, 15, 20, 1, 7, 25]

console.log('\nTesting generateAutoColorRanges with quantize scale (default)...');
const quantizeRanges = generateAutoColorRanges(extractedValues, 5, 'quantize');
console.log('Quantize ranges (simple & clean):', quantizeRanges);

// Show the actual min/max from data
console.log(`Data range: ${Math.min(...extractedValues)} to ${Math.max(...extractedValues)}`);

console.log('\nTesting generateAutoColorRanges with quantile scale...');
const quantileRanges = generateAutoColorRanges(extractedValues, 5, 'quantile');
console.log('Quantile ranges:', quantileRanges);

console.log('\nTesting generateAutoColorRanges with linear scale...');
const linearRanges = generateAutoColorRanges(extractedValues, 5, 'linear');
console.log('Linear ranges:', linearRanges);

console.log('\nTesting generateAutoColorRanges with threshold scale...');
const thresholdRanges = generateAutoColorRanges(extractedValues, 5, 'threshold');
console.log('Threshold ranges:', thresholdRanges);

// Test edge cases
console.log('\n=== EDGE CASE TESTING ===');

console.log('\n1. Single value (all same):');
const singleValueRanges = generateAutoColorRanges([10, 10, 10], 3, 'quantize');
console.log('Single value ranges:', singleValueRanges);

console.log('\n2. Empty array:');
const emptyRanges = generateAutoColorRanges([], 3, 'quantize');
console.log('Empty ranges:', emptyRanges);

console.log('\n3. Negative values:');
const negativeRanges = generateAutoColorRanges([-20, -15, -10, -5, 0, 5], 4, 'quantize');
console.log('Negative value ranges:', negativeRanges);

console.log('\n4. Values close to zero:');
const nearZeroRanges = generateAutoColorRanges([0.1, 0.5, 1.2, 2.8, 5.1], 3, 'quantize');
console.log('Near-zero ranges:', nearZeroRanges);

console.log('\n5. Large range with small values:');
const largeRangeRanges = generateAutoColorRanges([1, 2, 3, 1000], 4, 'quantize');
console.log('Large range ranges:', largeRangeRanges);

console.log('\n6. Very small decimal values:');
const smallDecimalRanges = generateAutoColorRanges([0.001, 0.002, 0.003, 0.004], 3, 'quantize');
console.log('Small decimal ranges:', smallDecimalRanges);

// Validation test
console.log('\n=== VALIDATION TESTING ===');
function validateTestRanges(ranges, testName) {
  console.log(`\nValidating ${testName}:`);

  if (ranges.length === 0) {
    console.log('✓ Empty ranges (valid for empty input)');
    return;
  }

  let hasErrors = false;

  // Check for zero-width ranges
  ranges.forEach((range, i) => {
    if (range.minValue >= range.maxValue) {
      console.log(`✗ Range ${i + 1} has zero width: ${range.minValue} >= ${range.maxValue}`);
      hasErrors = true;
    }
  });

  // Check for overlaps
  for (let i = 0; i < ranges.length - 1; i++) {
    if (ranges[i].maxValue > ranges[i + 1].minValue) {
      console.log(`✗ Range ${i + 1} overlaps with Range ${i + 2}: ${ranges[i].maxValue} > ${ranges[i + 1].minValue}`);
      hasErrors = true;
    }
  }

  // Check for gaps
  for (let i = 0; i < ranges.length - 1; i++) {
    if (ranges[i].maxValue < ranges[i + 1].minValue) {
      console.log(`✗ Gap between Range ${i + 1} and Range ${i + 2}: ${ranges[i].maxValue} < ${ranges[i + 1].minValue}`);
      hasErrors = true;
    }
  }

  if (!hasErrors) {
    console.log('✓ All ranges are valid (no overlaps, no zero-width, no gaps)');
  }
}

validateTestRanges(quantizeRanges, 'Quantize ranges');
validateTestRanges(singleValueRanges, 'Single value ranges');
validateTestRanges(negativeRanges, 'Negative value ranges');
validateTestRanges(nearZeroRanges, 'Near-zero ranges');

export { testStatsData, extractedValues, quantizeRanges, quantileRanges, linearRanges, thresholdRanges };
