import dayjs from 'dayjs';
import { ChartConfigurationFormInput, ChartSetting, paletteColors, RollingPeriod, ValueColorRange } from './chartTypes';
import { themeToColor } from '../../theme';
import { hasMetaMap, KeyTotal, StatsGroupBy, StatsGroupByFieldMetaMap } from '../stats/statsTypes';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getColor(key: string, fieldColorMapping?: Record<string, string>, groupBy?: StatsGroupBy): string {
  if (fieldColorMapping?.[key]) {
    return fieldColorMapping[key];
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

/**
 * Validates that color ranges don't overlap and are properly ordered
 */
export function validateColorRanges(ranges: ValueColorRange[]): string | null {
  if (ranges.length === 0) return null;

  // Sort ranges by minValue for validation
  const sortedRanges = [...ranges].sort((a, b) => a.minValue - b.minValue);

  for (let i = 0; i < sortedRanges.length; i += 1) {
    const range = sortedRanges[i];

    // Check if minValue is less than maxValue
    if (range.minValue >= range.maxValue) {
      return `Range ${i + 1}: Minimum value must be less than maximum value`;
    }

    // Check for overlaps with next range
    if (i < sortedRanges.length - 1) {
      const nextRange = sortedRanges[i + 1];
      if (range.maxValue > nextRange.minValue) {
        return `Ranges ${i + 1} and ${i + 2} overlap`;
      }
    }
  }

  return null;
}

/**
 * Sorts color ranges by minValue
 */
export function sortColorRanges(ranges: ValueColorRange[]): ValueColorRange[] {
  return [...ranges].sort((a, b) => a.minValue - b.minValue);
}

/**
 * Gets the color for a value based on defined color ranges
 * Falls back to a default color if no range matches
 */
export function getValueBasedColor(value: number, ranges: ValueColorRange[], fallbackColor = '#3182ce'): string {
  if (!ranges || ranges.length === 0) {
    return fallbackColor;
  }

  const sortedRanges = sortColorRanges(ranges);

  const matchingRange = sortedRanges.find(range =>
    value >= range.minValue && value <= range.maxValue
  );

  if (matchingRange) {
    return matchingRange.color;
  }

  return fallbackColor;
}

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};
