import dayjs from 'dayjs';
// eslint-disable-next-line import/no-extraneous-dependencies
import { scaleQuantile } from 'd3-scale';
import { ChartConfigurationFormInput, ChartSetting, paletteColors, RollingPeriod, ValueColorRange } from './chartTypes';
import { themeToColor } from '../../theme';
import { hasMetaMap, KeyTotal, StatsGroupBy, StatsGroupByFieldMetaMap, StatsKeyValue } from '../stats/statsTypes';

export default function getDateRangeFromRollingPeriod(period: RollingPeriod): { startDate: number; endDate: number } {
  const end = dayjs().endOf('day');
  let start: dayjs.Dayjs;

  switch (period) {
    case RollingPeriod.LAST_7_DAYS:
      start = end.subtract(7, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_30_DAYS:
      start = end.subtract(30, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_90_DAYS:
      start = end.subtract(90, 'days').startOf('day');
      break;
    case RollingPeriod.LAST_6_MONTHS:
      start = end.subtract(6, 'months').startOf('day');
      break;
    case RollingPeriod.LAST_YEAR:
      start = end.subtract(1, 'year').startOf('day');
      break;
    default:
      start = end.subtract(7, 'days').startOf('day');
  }

  return {
    startDate: start.valueOf(),
    endDate: end.valueOf(),
  };
}

export function randomThemeColor(): string {
  return themeToColor(paletteColors[Math.floor(Math.random() * paletteColors.length)]);
}

/**
 * Get a color for a key based on various color mapping strategies
 *
 * @param key The key to get a color for
 * @param fieldColorMapping Optional user-defined mapping of fields to colors
 * @param groupBy Optional grouping parameter to look up predefined colors from metadata
 * @returns The color for the key - either from fieldColorMapping, metadata, or a random theme color
 */
export function getColor(key: string, fieldColorMapping?: Record<string, string>, groupBy?: StatsGroupBy): string {
  if (fieldColorMapping?.[key]) {
    return fieldColorMapping[key];
  }

  if (!groupBy || !hasMetaMap(groupBy)) {
    return randomThemeColor();
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return randomThemeColor();
  }

  return metaValue.color;
}
/**
 * Get a display name for a key based on metadata mapping
 *
 * @param key The key to get a display name for
 * @param groupBy Optional grouping parameter to look up predefined names from metadata
 * @returns The display name for the key - either from metadata or the original key
 */
export function getFieldName(key: string, groupBy?: StatsGroupBy): string {
  if (!groupBy || !hasMetaMap(groupBy)) {
    return key;
  }

  const metaValue = StatsGroupByFieldMetaMap[groupBy][key];
  if (!metaValue) {
    return key;
  }

  return metaValue.label;
}

// helper to elide with an ellipsis:
export const truncate = (s: string, max: number) => (max > 0 && s.length > max ? `${s.slice(0, max)}…` : s);

// ? Helper to extract the value for a given key
export const extractY = (total: number | KeyTotal[], key: string) => {
  const items = Array.isArray(total) ? total : [{ key, total }];
  return items.find((item) => item.key === key)?.total ?? 0;
};

export const convertChartSettingToForm = (chartSetting: ChartSetting): ChartConfigurationFormInput => ({
  ...chartSetting,
});

/**
 * Validates that color ranges don't overlap and are properly ordered
 */
export function validateColorRanges(ranges: ValueColorRange[]): string | null {
  if (ranges.length === 0) return null;

  // Sort ranges by minValue for validation
  const sortedRanges = [...ranges].sort((a, b) => a.minValue - b.minValue);

  for (let i = 0; i < sortedRanges.length; i += 1) {
    const range = sortedRanges[i];

    // Check if minValue is less than maxValue
    if (range.minValue >= range.maxValue) {
      return `Range ${i + 1}: Minimum value must be less than maximum value`;
    }

    // Check for overlaps with next range
    if (i < sortedRanges.length - 1) {
      const nextRange = sortedRanges[i + 1];
      if (range.maxValue > nextRange.minValue) {
        return `Ranges ${i + 1} and ${i + 2} overlap`;
      }
    }
  }

  return null;
}

/**
 * Sorts color ranges by minValue
 */
export function sortColorRanges(ranges: ValueColorRange[]): ValueColorRange[] {
  return [...ranges].sort((a, b) => a.minValue - b.minValue);
}

/**
 * Gets the color for a value based on defined color ranges
 * Falls back to a default color if no range matches
 */
export function getValueBasedColor(value: number, ranges: ValueColorRange[], fallbackColor = '#3182ce'): string {
  if (!ranges || ranges.length === 0) {
    return fallbackColor;
  }

  const sortedRanges = sortColorRanges(ranges);

  const matchingRange = sortedRanges.find(range =>
    value >= range.minValue && value <= range.maxValue
  );

  if (matchingRange) {
    return matchingRange.color;
  }

  return fallbackColor;
}

/**
 * Extracts all numerical values from pivot table stats data
 */
export function extractValuesFromStatsData(statsData: StatsKeyValue): number[] {
  const values: number[] = [];

  function extractRecursive(data: StatsKeyValue) {
    Object.values(data).forEach((value) => {
      if (typeof value === 'object') {
        if ('count' in value && typeof value.count === 'number') {
          // This is a CountObject with a count property
          values.push(value.count);
        } else {
          // This is nested StatsKeyValue, recurse
          extractRecursive(value as StatsKeyValue);
        }
      }
    });
  }

  extractRecursive(statsData);
  return values.filter(v => typeof v === 'number' && !Number.isNaN(v));
}

/**
 * Rounds a number to a nice, clean integer
 */
function roundToNiceNumber(value: number, roundUp = false): number {
  if (value <= 0) return 0;

  // For small numbers, just round normally
  if (value < 10) {
    return roundUp ? Math.ceil(value) : Math.floor(value);
  }

  // For larger numbers, round to nice multiples
  const magnitude = 10 ** Math.floor(Math.log10(value));
  const normalized = value / magnitude;

  let multiplier: number;
  if (normalized <= 1) multiplier = 1;
  else if (normalized <= 2) multiplier = 2;
  else if (normalized <= 5) multiplier = 5;
  else multiplier = 10;

  const base = multiplier * magnitude;
  return roundUp ? Math.ceil(value / base) * base : Math.floor(value / base) * base;
}

/**
 * Creates clean, round number thresholds
 */
function createCleanThresholds(min: number, max: number, count: number): number[] {
  if (count <= 1) return [Math.ceil(max)];

  // Start with a nice round number at or below min
  const cleanMin = roundToNiceNumber(min, false);
  // End with a nice round number at or above max
  const cleanMax = roundToNiceNumber(max, true);

  const range = cleanMax - cleanMin;
  const rawStep = range / count;

  // Round step to a nice number
  const cleanStep = roundToNiceNumber(rawStep, true);

  const thresholds: number[] = [];
  let current = cleanMin;

  Array.from({ length: count }, (_, i) => {
    if (i === count - 1) {
      // Last threshold should be the clean max
      thresholds.push(cleanMax);
    } else {
      current += cleanStep;
      thresholds.push(current);
    }
    return null;
  });

  return thresholds;
}

/**
 * Generates automatic color ranges using d3-scale based on data distribution
 */
export function generateAutoColorRanges(
  values: number[],
  numberOfRanges = 5,
  scaleType: 'quantize' | 'quantile' | 'linear' | 'threshold' = 'quantize'
): ValueColorRange[] {
  if (values.length === 0) {
    return [];
  }

  const sortedValues = [...values].sort((a, b) => a - b);
  const minValue = sortedValues[0];
  const maxValue = sortedValues[sortedValues.length - 1];

  // If all values are the same, create a single range
  if (minValue === maxValue) {
    return [{
      minValue,
      maxValue,
      color: themeToColor(paletteColors[0]),
    }];
  }

  const ranges: ValueColorRange[] = [];
  const colorCount = Math.min(numberOfRanges, paletteColors.length);

  switch (scaleType) {
    case 'quantize': {
      // Generate clean, round number thresholds
      const thresholds = createCleanThresholds(minValue, maxValue, colorCount);

      // Start from a clean round number
      let currentMin = roundToNiceNumber(minValue, false);

      thresholds.forEach((threshold, index) => {
        ranges.push({
          minValue: currentMin,
          maxValue: threshold,
          color: themeToColor(paletteColors[index]),
        });
        currentMin = threshold;
      });

      break;
    }

    case 'quantile': {
      // Use quantile scale for equal distribution of data points
      const quantileScale = scaleQuantile<string>()
        .domain(sortedValues)
        .range(paletteColors.slice(0, colorCount));

      const quantiles = quantileScale.quantiles();

      // Create ranges based on quantiles
      let prevValue = minValue;
      quantiles.forEach((quantile, index) => {
        ranges.push({
          minValue: prevValue,
          maxValue: quantile,
          color: themeToColor(paletteColors[index]),
        });
        prevValue = quantile;
      });

      // Add final range
      if (prevValue < maxValue) {
        ranges.push({
          minValue: prevValue,
          maxValue,
          color: themeToColor(paletteColors[Math.min(quantiles.length, colorCount - 1)]),
        });
      }
      break;
    }

    case 'linear': {
      // Use linear scale for equal intervals
      const step = (maxValue - minValue) / colorCount;

      Array.from({ length: colorCount }, (_, i) => {
        const rangeMin = minValue + (step * i);
        const rangeMax = i === colorCount - 1 ? maxValue : minValue + (step * (i + 1));

        ranges.push({
          minValue: rangeMin,
          maxValue: rangeMax,
          color: themeToColor(paletteColors[i]),
        });
        return null;
      });
      break;
    }

    case 'threshold': {
      // Use threshold scale with natural breaks
      const thresholds: number[] = [];

      // Calculate natural breaks using Jenks-like algorithm (simplified)
      Array.from({ length: colorCount - 1 }, (_, i) => {
        const thresholdIndex = Math.floor((sortedValues.length * (i + 1)) / colorCount);
        thresholds.push(sortedValues[thresholdIndex]);
        return null;
      });

      let prevThreshold = minValue;
      thresholds.forEach((threshold, index) => {
        ranges.push({
          minValue: prevThreshold,
          maxValue: threshold,
          color: themeToColor(paletteColors[index]),
        });
        prevThreshold = threshold;
      });

      // Add final range
      ranges.push({
        minValue: prevThreshold,
        maxValue,
        color: themeToColor(paletteColors[Math.min(thresholds.length, colorCount - 1)]),
      });
      break;
    }

    default:
      throw new Error(`Unsupported scale type: ${scaleType}`);
  }

  return ranges;
}

export const convertFormToChartSetting = (form: ChartConfigurationFormInput): ChartSetting => {
  const { fieldArrayId, ...rest } = form;
  const entries = Object.entries(rest).filter(([, v]) => v != null);
  return Object.fromEntries(entries) as unknown as ChartSetting;
};
