// Manual test script for auto color range generation
// This can be run in the browser console to test the functionality

import { extractValuesFromStatsData, generateAutoColorRanges } from './chartFunctions';

// Test data that mimics real stats data structure
const testStatsData = {
  'High': {
    'Critical': { count: 5 },
    'Major': { count: 12 },
    'Minor': { count: 8 },
  },
  'Medium': {
    'Critical': { count: 3 },
    'Major': { count: 15 },
    'Minor': { count: 20 },
  },
  'Low': {
    'Critical': { count: 1 },
    'Major': { count: 7 },
    'Minor': { count: 25 },
  },
};

console.log('Testing extractValuesFromStatsData...');
const extractedValues = extractValuesFromStatsData(testStatsData);
console.log('Extracted values:', extractedValues);
// Expected: [5, 12, 8, 3, 15, 20, 1, 7, 25]

console.log('\nTesting generateAutoColorRanges with quantize scale (default)...');
const quantizeRanges = generateAutoColorRanges(extractedValues, 5, 'quantize');
console.log('Quantize ranges (clean thresholds):', quantizeRanges);

console.log('\nTesting generateAutoColorRanges with quantile scale...');
const quantileRanges = generateAutoColorRanges(extractedValues, 5, 'quantile');
console.log('Quantile ranges:', quantileRanges);

console.log('\nTesting generateAutoColorRanges with linear scale...');
const linearRanges = generateAutoColorRanges(extractedValues, 5, 'linear');
console.log('Linear ranges:', linearRanges);

console.log('\nTesting generateAutoColorRanges with threshold scale...');
const thresholdRanges = generateAutoColorRanges(extractedValues, 5, 'threshold');
console.log('Threshold ranges:', thresholdRanges);

// Test edge cases
console.log('\nTesting edge case: single value...');
const singleValueRanges = generateAutoColorRanges([10, 10, 10], 3, 'quantile');
console.log('Single value ranges:', singleValueRanges);

console.log('\nTesting edge case: empty array...');
const emptyRanges = generateAutoColorRanges([], 3, 'quantile');
console.log('Empty ranges:', emptyRanges);

export { testStatsData, extractedValues, quantizeRanges, quantileRanges, linearRanges, thresholdRanges };
